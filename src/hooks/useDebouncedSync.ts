import { useEffect, useRef, useCallback } from 'react';
import { useGenerationStore } from '@/stores/generationStore';
import { useWebContainerIntegration } from '@/hooks/useWebContainerIntegration';

interface DebouncedSyncOptions {
	delay?: number;
	maxWait?: number;
	enabled?: boolean;
	fileFilter?: (fileId: string) => boolean;
}

/**
 * Hook for debounced synchronization with WebContainer
 * Uses Zustand subscriptions to watch for changes and sync automatically
 */
export const useDebouncedSync = (options: DebouncedSyncOptions = {}) => {
	const {
		delay = 1000,
		maxWait = 5000,
		enabled = true,
		fileFilter = () => true,
	} = options;

	const timeoutRef = useRef<NodeJS.Timeout | null>(null);
	const maxWaitTimeoutRef = useRef<NodeJS.Timeout | null>(null);
	const lastSyncRef = useRef<number>(0);
	const pendingFilesRef = useRef<Set<string>>(new Set());

	const { updateFileInWebContainer, isWebContainerOperational } =
		useWebContainerIntegration(enabled);
	const generationStore = useGenerationStore();

	// Safe destructuring with defaults
	const {
		queueForSync = () => {},
		syncToWebContainer = () => {},
		isAutoSyncEnabled = false,
	} = generationStore || {};

	// Check if sync is actually enabled
	const isSyncEnabled = enabled && !!generationStore && isAutoSyncEnabled;

	// Clear timeouts on unmount
	useEffect(() => {
		return () => {
			if (timeoutRef.current) {
				clearTimeout(timeoutRef.current);
			}
			if (maxWaitTimeoutRef.current) {
				clearTimeout(maxWaitTimeoutRef.current);
			}
		};
	}, []);

	// Perform sync for pending files
	const performSync = useCallback(async () => {
		if (!isSyncEnabled || !isWebContainerOperational()) {
			return;
		}

		const filesToSync = Array.from(pendingFilesRef.current);
		if (filesToSync.length === 0) {
			return;
		}

		console.log('[DebouncedSync] Syncing files:', filesToSync);

		// Clear pending files
		pendingFilesRef.current.clear();

		// Clear timeouts
		if (timeoutRef.current) {
			clearTimeout(timeoutRef.current);
			timeoutRef.current = null;
		}
		if (maxWaitTimeoutRef.current) {
			clearTimeout(maxWaitTimeoutRef.current);
			maxWaitTimeoutRef.current = null;
		}

		// Sync each file
		for (const fileId of filesToSync) {
			try {
				const success = await updateFileInWebContainer(fileId);
				if (success) {
					syncToWebContainer(fileId);
				}
			} catch (error) {
				console.error(`[DebouncedSync] Failed to sync file ${fileId}:`, error);
			}
		}

		lastSyncRef.current = Date.now();
	}, [
		isSyncEnabled,
		isWebContainerOperational,
		updateFileInWebContainer,
		syncToWebContainer,
	]);

	// Schedule sync with debouncing
	const scheduleSync = useCallback(
		(fileId: string) => {
			if (!isSyncEnabled || !fileFilter(fileId)) {
				return;
			}

			// Add file to pending list
			pendingFilesRef.current.add(fileId);
			queueForSync(fileId);

			// Clear existing timeout
			if (timeoutRef.current) {
				clearTimeout(timeoutRef.current);
			}

			// Set new timeout
			timeoutRef.current = setTimeout(performSync, delay);

			// Set max wait timeout if not already set
			if (!maxWaitTimeoutRef.current) {
				maxWaitTimeoutRef.current = setTimeout(() => {
					console.log('[DebouncedSync] Max wait reached, forcing sync');
					performSync();
				}, maxWait);
			}
		},
		[
			enabled,
			isAutoSyncEnabled,
			fileFilter,
			queueForSync,
			delay,
			maxWait,
			performSync,
		],
	);

	// Subscribe to file content changes
	useEffect(() => {
		const unsubscribe = useGenerationStore.subscribe(
			(state) => state.editableContent,
			(editableContent, previousEditableContent) => {
				// Find changed files
				const changedFiles = Object.keys(editableContent).filter((fileId) => {
					const currentContent = editableContent[fileId];
					const previousContent = previousEditableContent[fileId];
					return (
						currentContent !== previousContent && currentContent !== undefined
					);
				});

				// Schedule sync for changed files
				changedFiles.forEach((fileId) => {
					scheduleSync(fileId);
				});
			},
			{
				equalityFn: (a, b) => {
					// Custom equality function to detect content changes
					const aKeys = Object.keys(a);
					const bKeys = Object.keys(b);

					if (aKeys.length !== bKeys.length) return false;

					return aKeys.every((key) => a[key] === b[key]);
				},
			},
		);

		return unsubscribe;
	}, [scheduleSync]);

	// Force sync all pending files
	const forceSyncAll = useCallback(async () => {
		if (timeoutRef.current) {
			clearTimeout(timeoutRef.current);
			timeoutRef.current = null;
		}
		if (maxWaitTimeoutRef.current) {
			clearTimeout(maxWaitTimeoutRef.current);
			maxWaitTimeoutRef.current = null;
		}
		await performSync();
	}, [performSync]);

	// Force sync specific file immediately
	const forceSyncFile = useCallback(
		async (fileId: string) => {
			if (!isWebContainerOperational() || !enabled) {
				return false;
			}

			try {
				const success = await updateFileInWebContainer(fileId);
				if (success) {
					syncToWebContainer(fileId);
					// Remove from pending files
					pendingFilesRef.current.delete(fileId);
				}
				return success;
			} catch (error) {
				console.error(
					`[DebouncedSync] Failed to force sync file ${fileId}:`,
					error,
				);
				return false;
			}
		},
		[
			isWebContainerOperational,
			enabled,
			updateFileInWebContainer,
			syncToWebContainer,
		],
	);

	// Get sync status
	const getSyncStatus = useCallback(() => {
		return {
			pendingFiles: Array.from(pendingFilesRef.current),
			hasPendingSync: pendingFilesRef.current.size > 0,
			lastSyncTime: lastSyncRef.current,
			isEnabled: isSyncEnabled,
			isOperational: isWebContainerOperational(),
		};
	}, [isSyncEnabled, isWebContainerOperational]);

	return {
		// Actions
		forceSyncAll,
		forceSyncFile,
		scheduleSync,

		// Status
		getSyncStatus,

		// Config
		isEnabled: isSyncEnabled,
		delay,
		maxWait,
	};
};

/**
 * Hook for monitoring sync conflicts
 */
export const useSyncConflictDetection = () => {
	const { editableContent, lastSyncedContent } = useGenerationStore();

	// Detect conflicts between local and synced content
	const detectConflicts = useCallback(() => {
		const conflicts: Array<{
			fileId: string;
			localContent: string;
			syncedContent: string;
			hasConflict: boolean;
		}> = [];

		Object.keys(editableContent).forEach((fileId) => {
			const localContent = editableContent[fileId];
			const syncedContent = lastSyncedContent[fileId];

			if (syncedContent && localContent !== syncedContent) {
				conflicts.push({
					fileId,
					localContent,
					syncedContent,
					hasConflict: true,
				});
			}
		});

		return conflicts;
	}, [editableContent, lastSyncedContent]);

	// Get files that need sync
	const getFilesNeedingSync = useCallback(() => {
		return Object.keys(editableContent).filter((fileId) => {
			const localContent = editableContent[fileId];
			const syncedContent = lastSyncedContent[fileId];
			return localContent !== syncedContent;
		});
	}, [editableContent, lastSyncedContent]);

	return {
		detectConflicts,
		getFilesNeedingSync,
		hasConflicts: detectConflicts().length > 0,
		conflictCount: detectConflicts().length,
	};
};
