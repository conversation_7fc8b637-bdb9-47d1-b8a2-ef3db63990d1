import { create } from 'zustand';
import { devtools, persist, subscribeWithSelector } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import { enableMapSet } from 'immer';
import type {
	GenerationStore,
	GenerationState,
	FileSystemNode,
	GenerationMetadata,
} from '@/types/generation';
import { DEFAULT_WEBCONTAINER_STATE } from '@/types/generation';

// Enable MapSet plugin for Immer to work with Set objects
enableMapSet();

// Initial state
const initialState: GenerationState = {
	// File system state
	files: [],
	activeFileId: '',
	editableContent: {},
	originalContent: {},

	// UI state
	activePanel: 'code',
	isStreaming: false,
	streamingContent: '',
	copiedFileId: null,

	// WebContainer state
	webContainer: DEFAULT_WEBCONTAINER_STATE,

	// Sync state
	lastSyncedContent: {},
	isDirty: false,
	dirtyFiles: new Set(),
	syncQueue: new Set(),
	isAutoSyncEnabled: true,
	lastSyncTimestamp: 0,

	// Generation metadata
	metadata: null,
};

// Store validation utility
export const validateGenerationStore = (
	store: any,
): store is GenerationStore => {
	return store && typeof store === 'object' && 'files' in store;
};

export const useGenerationStore = create<GenerationStore>()(
	subscribeWithSelector(
		devtools(
			persist(
				immer((set, get) => ({
					...initialState,

					// File operations
					setFiles: (files: FileSystemNode[]) =>
						set((state) => {
							state.files = files;
							// Set default active file to App.tsx if available
							const appFile = files.find((f) => f.name === 'src/App.tsx');
							if (appFile && !state.activeFileId) {
								state.activeFileId = appFile.id;
							}
						}),

					clearFiles: () =>
						set((state) => {
							state.files = [];
							state.activeFileId = '';
							state.editableContent = {};
							state.originalContent = {};
							state.dirtyFiles.clear();
							state.isDirty = false;
							state.lastSyncedContent = {};
							state.syncQueue.clear();
						}),

					setActiveFile: (fileId: string) =>
						set((state) => {
							state.activeFileId = fileId;
						}),

					updateFileContent: (fileId: string, content: string) =>
						set((state) => {
							state.editableContent[fileId] = content;
							state.dirtyFiles.add(fileId);
							state.isDirty = state.dirtyFiles.size > 0;
						}),

					resetFileContent: (fileId: string) =>
						set((state) => {
							const originalContent = state.originalContent[fileId];
							if (originalContent) {
								state.editableContent[fileId] = originalContent;
							} else {
								delete state.editableContent[fileId];
							}
							state.dirtyFiles.delete(fileId);
							state.isDirty = state.dirtyFiles.size > 0;
						}),

					markFileDirty: (fileId: string) =>
						set((state) => {
							state.dirtyFiles.add(fileId);
							state.isDirty = true;
						}),

					markFileClean: (fileId: string) =>
						set((state) => {
							state.dirtyFiles.delete(fileId);
							state.isDirty = state.dirtyFiles.size > 0;
						}),

					// UI operations
					switchPanel: (panel: 'code' | 'preview') =>
						set((state) => {
							state.activePanel = panel;
						}),

					setCopiedFile: (fileId: string | null) =>
						set((state) => {
							state.copiedFileId = fileId;
						}),

					// Streaming operations
					startStreaming: (content: string) =>
						set((state) => {
							state.isStreaming = true;
							state.streamingContent = content;
						}),

					updateStreaming: (content: string) =>
						set((state) => {
							state.streamingContent = content;
						}),

					stopStreaming: () =>
						set((state) => {
							state.isStreaming = false;
							// Move streaming content to editable content for App.tsx
							const appFile = state.files.find((f) => f.name === 'src/App.tsx');
							if (appFile && state.streamingContent) {
								state.editableContent[appFile.id] = state.streamingContent;
								state.dirtyFiles.add(appFile.id);
								state.isDirty = true;
							}
							state.streamingContent = '';
						}),

					// WebContainer operations
					setWebContainerBooting: () =>
						set((state) => {
							state.webContainer.isBooting = true;
							state.webContainer.error = null;
						}),

					setWebContainerReady: (url?: string) =>
						set((state) => {
							state.webContainer.isReady = true;
							state.webContainer.isBooting = false;
							state.webContainer.isInstalling = false;
							state.webContainer.isStarting = false;
							if (url) {
								state.webContainer.url = url;
							}
						}),

					setWebContainerError: (error: string) =>
						set((state) => {
							state.webContainer.error = error;
							state.webContainer.isBooting = false;
							state.webContainer.isInstalling = false;
							state.webContainer.isStarting = false;
						}),

					setWebContainerInstalling: () =>
						set((state) => {
							state.webContainer.isInstalling = true;
							state.webContainer.error = null;
						}),

					setWebContainerStarting: () =>
						set((state) => {
							state.webContainer.isStarting = true;
							state.webContainer.error = null;
						}),

					setWebContainerInitialized: () =>
						set((state) => {
							state.webContainer.hasInitialized = true;
						}),

					// Sync operations
					syncToWebContainer: (fileId: string) =>
						set((state) => {
							const content = state.editableContent[fileId];
							if (content) {
								state.lastSyncedContent[fileId] = content;
								state.syncQueue.delete(fileId);
								state.lastSyncTimestamp = Date.now();
							}
						}),

					syncAllToWebContainer: () =>
						set((state) => {
							Object.keys(state.editableContent).forEach((fileId) => {
								state.lastSyncedContent[fileId] = state.editableContent[fileId];
							});
							state.syncQueue.clear();
							state.lastSyncTimestamp = Date.now();
						}),

					markSynced: (fileId: string, content: string) =>
						set((state) => {
							state.lastSyncedContent[fileId] = content;
							state.syncQueue.delete(fileId);
							state.lastSyncTimestamp = Date.now();
						}),

					// Queue file for sync
					queueForSync: (fileId: string) =>
						set((state) => {
							if (state.isAutoSyncEnabled) {
								state.syncQueue.add(fileId);
							}
						}),

					// Toggle auto-sync
					toggleAutoSync: () =>
						set((state) => {
							state.isAutoSyncEnabled = !state.isAutoSyncEnabled;
							if (!state.isAutoSyncEnabled) {
								state.syncQueue.clear();
							}
						}),

					// Check if file needs sync
					needsSync: (fileId: string) => {
						const state = get();
						const currentContent = state.editableContent[fileId];
						const lastSyncedContent = state.lastSyncedContent[fileId];
						return currentContent !== lastSyncedContent;
					},

					// Metadata operations
					setMetadata: (metadata: GenerationMetadata) =>
						set((state) => {
							state.metadata = metadata;
						}),

					// Computed getters
					getActiveFile: () => {
						const state = get();
						return state.files.find((f) => f.id === state.activeFileId) || null;
					},

					getFileContent: (fileId: string) => {
						const state = get();
						return (
							state.editableContent[fileId] ||
							state.files.find((f) => f.id === fileId)?.content ||
							''
						);
					},

					isFileDirty: (fileId: string) => {
						const state = get();
						return state.dirtyFiles.has(fileId);
					},

					hasUnsavedChanges: () => {
						const state = get();
						return state.isDirty;
					},

					canSyncToWebContainer: () => {
						const state = get();
						return state.webContainer.isReady && state.isDirty;
					},

					getFileByPath: (path: string) => {
						const state = get();
						return state.files.find((f) => f.path === path) || null;
					},
				})),
				{
					name: 'generation-store',
					partialize: () => ({}), // No state persistence needed
				},
			),
			{
				name: 'generation-store',
			},
		),
	),
);
