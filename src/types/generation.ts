/**
 * Types for Generation Workspace State Management
 */

export interface FileSystemNode {
	id: string;
	name: string;
	type: 'file' | 'folder';
	language?: string;
	content?: string;
	children?: FileSystemNode[];
	path: string;
	isEditable: boolean;
}

export interface GenerationMetadata {
	model?: string;
	processingTime?: number;
	tokensGenerated?: number;
	framework?: string;
	styling?: string;
	language?: string;
	error?: boolean;
	errorMessage?: string;
	files?: string[];
}

export interface WebContainerState {
	isReady: boolean;
	isBooting: boolean;
	isInstalling: boolean;
	isStarting: boolean;
	url: string | null;
	error: string | null;
	hasInitialized: boolean;
}

export interface GenerationState {
	// File system state
	files: FileSystemNode[];
	activeFileId: string;
	editableContent: Record<string, string>;
	originalContent: Record<string, string>;

	// UI state
	activePanel: 'code' | 'preview';
	isStreaming: boolean;
	streamingContent: string;
	copiedFileId: string | null;

	// WebContainer state
	webContainer: WebContainerState;

	// Sync state
	lastSyncedContent: Record<string, string>;
	isDirty: boolean;
	dirtyFiles: Set<string>;
	syncQueue: Set<string>;
	isAutoSyncEnabled: boolean;
	lastSyncTimestamp: number;

	// Generation metadata
	metadata: GenerationMetadata | null;
}

export interface GenerationActions {
	// File operations
	setFiles: (files: FileSystemNode[]) => void;
	clearFiles: () => void;
	setActiveFile: (fileId: string) => void;
	updateFileContent: (fileId: string, content: string) => void;
	resetFileContent: (fileId: string) => void;
	markFileDirty: (fileId: string) => void;
	markFileClean: (fileId: string) => void;

	// UI operations
	switchPanel: (panel: 'code' | 'preview') => void;
	setCopiedFile: (fileId: string | null) => void;

	// Streaming operations
	startStreaming: (content: string) => void;
	updateStreaming: (content: string) => void;
	stopStreaming: () => void;

	// WebContainer operations
	setWebContainerBooting: () => void;
	setWebContainerReady: (url?: string) => void;
	setWebContainerError: (error: string) => void;
	setWebContainerInstalling: () => void;
	setWebContainerStarting: () => void;
	setWebContainerInitialized: () => void;

	// Sync operations
	syncToWebContainer: (fileId: string) => void;
	syncAllToWebContainer: () => void;
	markSynced: (fileId: string, content: string) => void;
	queueForSync: (fileId: string) => void;
	toggleAutoSync: () => void;
	needsSync: (fileId: string) => boolean;

	// Metadata operations
	setMetadata: (metadata: GenerationMetadata) => void;

	// Computed getters
	getActiveFile: () => FileSystemNode | null;
	getFileContent: (fileId: string) => string;
	isFileDirty: (fileId: string) => boolean;
	hasUnsavedChanges: () => boolean;
	canSyncToWebContainer: () => boolean;
	getFileByPath: (path: string) => FileSystemNode | null;
}

export type GenerationStore = GenerationState & GenerationActions;

// Default WebContainer state
export const DEFAULT_WEBCONTAINER_STATE: WebContainerState = {
	isReady: false,
	isBooting: false,
	isInstalling: false,
	isStarting: false,
	url: null,
	error: null,
	hasInitialized: false,
};
