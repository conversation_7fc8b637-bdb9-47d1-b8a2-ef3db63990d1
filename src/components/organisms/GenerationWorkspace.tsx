import React, { useEffect, useState, useRef, useMemo } from 'react';
import { useFileOperations } from '@/hooks/useFileOperations';
import { useWebContainerIntegration } from '@/hooks/useWebContainerIntegration';
import { useDebouncedSync } from '@/hooks/useDebouncedSync';
import { useGenerationStore } from '@/stores/generationStore';
import {
	useGeneration,
	useConversation,
	useGenerationStream,
} from '@/hooks/useGeneration';
import { FileExplorer, CodeEditor, PreviewPanel } from '@/components/molecules';
import { ChatPanel } from '@/components/templates/generations/ChatPanel';
import { Resizable } from 're-resizable';

interface GenerationWorkspaceProps {
	generationId: string;
	generationResult: string;
	metadata?: Record<string, unknown> | null;
	isStreaming?: boolean;
	streamingResult?: string;
	onCodeUpdate?: (code: string) => void;
	className?: string;
}

export const GenerationWorkspace: React.FC<GenerationWorkspaceProps> = ({
	generationId,
	generationResult,
	metadata,
	isStreaming = false,
	streamingResult,
	onCodeUpdate,
	className = '',
}) => {
	const [isGenerating, setIsGenerating] = useState(false);
	const { initializeFiles } = useFileOperations();
	// Get generation data and conversation first
	const { generation } = useGeneration(generationId);
	const { messages, addMessage, fetchConversation } =
		useConversation(generationId);

	// Use SSE for real-time updates
	const { status: streamStatus, lastEvent } = useGenerationStream(generationId);

	// Determine if this is a documentation generation
	const isDocumentationMode = generation?.type === 'DOCUMENTATION';

	// Only enable WebContainer for UI generations
	const {
		autoSyncToWebContainer,
		updateAllFilesInWebContainer,
		isWebContainerOperational,
	} = useWebContainerIntegration(!isDocumentationMode);

	useDebouncedSync({
		delay: 1000,
		maxWait: 3000,
		enabled: !isDocumentationMode, // Disable for documentation generations
		fileFilter: (fileId) => fileId === 'app-tsx' || fileId === 'index-css', // Only sync editable files
	});

	const { startStreaming, updateStreaming, stopStreaming, setMetadata } =
		useGenerationStore();

	// Memoize the initialization key to prevent unnecessary re-initializations
	const initializationKey = useMemo(() => {
		return `${generationResult?.slice(0, 100)}-${JSON.stringify(metadata)}`;
	}, [generationResult, metadata]);

	// Track the last initialization to prevent loops
	const lastInitializationRef = useRef<string>('');

	// Initialize workspace when generation data changes
	useEffect(() => {
		if (
			generationResult &&
			initializationKey !== lastInitializationRef.current
		) {
			lastInitializationRef.current = initializationKey;

			initializeFiles(generationResult, metadata || undefined);
			if (metadata) {
				setMetadata(metadata); // Metadata from props
			}
		}
	}, [
		generationResult,
		metadata,
		initializeFiles,
		setMetadata,
		initializationKey,
	]);

	// Separate effect for WebContainer sync to avoid dependency loops
	const lastWebContainerSyncRef = useRef<string>('');
	useEffect(() => {
		if (
			generationResult &&
			!isDocumentationMode &&
			isWebContainerOperational() &&
			initializationKey !== lastWebContainerSyncRef.current
		) {
			console.log('🔄 Generation result updated, syncing to WebContainer');
			lastWebContainerSyncRef.current = initializationKey;

			const timeoutId = setTimeout(() => {
				updateAllFilesInWebContainer();
			}, 500);

			return () => clearTimeout(timeoutId);
		}
	}, [
		generationResult,
		isDocumentationMode,
		isWebContainerOperational,
		updateAllFilesInWebContainer,
		initializationKey,
	]);

	// Handle SSE events to refresh conversation when assistant responds
	const lastEventRef = useRef<typeof lastEvent>(null);
	useEffect(() => {
		// Prevent processing the same event multiple times
		if (lastEvent && lastEvent !== lastEventRef.current) {
			lastEventRef.current = lastEvent;

			if (lastEvent.type === 'completed' && lastEvent.result) {
				console.log('🔄 Assistant response completed, refreshing conversation');

				// First, refresh conversation to show the new assistant message
				fetchConversation()
					.then(() => {
						console.log('✅ Conversation refreshed, stopping loading state');
						// Clear any pending loading timeout
						if (loadingTimeoutRef.current) {
							clearTimeout(loadingTimeoutRef.current);
							loadingTimeoutRef.current = null;
						}
						setIsGenerating(false);

						if (!isDocumentationMode && isWebContainerOperational()) {
							console.log('🔄 Refreshing WebContainer with new UI generation');
							// Use multiple attempts to ensure WebContainer gets the latest code
							setTimeout(() => {
								console.log('🔄 First WebContainer refresh attempt');
								updateAllFilesInWebContainer();
							}, 1500);

							setTimeout(() => {
								console.log('🔄 Second WebContainer refresh attempt');
								updateAllFilesInWebContainer();
							}, 3000);
						}
					})
					.catch((error) => {
						console.error('Error refreshing conversation:', error);
						setIsGenerating(false);
					});
			} else if (lastEvent.type === 'progress' || lastEvent.type === 'status') {
				// Update generating state based on stream status
				const isInProgress =
					streamStatus === 'IN_PROGRESS' || streamStatus === 'PENDING';
				setIsGenerating(isInProgress);
				console.log(
					`📡 SSE Status: ${streamStatus}, Generating: ${isInProgress}`,
				);
			} else if (lastEvent.type === 'failed') {
				console.error('❌ SSE Generation Failed:', lastEvent);
				setIsGenerating(false);
			}
		}
	}, [
		lastEvent,
		fetchConversation,
		streamStatus,
		isDocumentationMode,
		isWebContainerOperational,
		updateAllFilesInWebContainer,
	]);

	// Handle streaming updates
	useEffect(() => {
		if (isStreaming && streamingResult) {
			if (!useGenerationStore.getState().isStreaming) {
				startStreaming(streamingResult);
			} else {
				updateStreaming(streamingResult);
			}
		} else if (!isStreaming && useGenerationStore.getState().isStreaming) {
			stopStreaming();
		}
	}, [
		isStreaming,
		streamingResult,
		startStreaming,
		updateStreaming,
		stopStreaming,
	]);

	const codeChangeTimeoutRef = useRef<NodeJS.Timeout | null>(null);
	const handleCodeChange = (fileId: string, content: string) => {
		if (fileId === 'app-tsx' && !isDocumentationMode) {
			autoSyncToWebContainer(fileId);
			onCodeUpdate?.(content);

			if (isWebContainerOperational()) {
				if (codeChangeTimeoutRef.current) {
					clearTimeout(codeChangeTimeoutRef.current);
				}

				codeChangeTimeoutRef.current = setTimeout(() => {
					console.log('🔄 Code changed, refreshing WebContainer preview');
					updateAllFilesInWebContainer();
				}, 300);
			}
		}
	};

	const loadingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

	useEffect(() => {
		return () => {
			if (codeChangeTimeoutRef.current) {
				clearTimeout(codeChangeTimeoutRef.current);
			}
			if (loadingTimeoutRef.current) {
				clearTimeout(loadingTimeoutRef.current);
			}
		};
	}, []);

	const handleSendMessage = async (message: string) => {
		if (!generation) return;

		setIsGenerating(true);

		loadingTimeoutRef.current = setTimeout(() => {
			console.warn('⚠️ Loading timeout reached, stopping loading state');
			setIsGenerating(false);
		}, 60000); // 60 second timeout

		try {
			await addMessage({
				content: message,
				inputData: { timestamp: Date.now() },
			});
			// Don't set isGenerating to false here - let SSE handle it
			// The SSE will update the state when the assistant responds
		} catch (error) {
			console.error('Error sending message:', error);
			if (loadingTimeoutRef.current) {
				clearTimeout(loadingTimeoutRef.current);
			}
			setIsGenerating(false);
		}
	};

	const handleRegenerateCode = async (prompt: string) => {
		if (!generation) return;

		setIsGenerating(true);

		// Set a timeout to prevent stuck loading state
		loadingTimeoutRef.current = setTimeout(() => {
			console.warn('⚠️ Regenerate timeout reached, stopping loading state');
			setIsGenerating(false);
		}, 60000); // 60 second timeout

		try {
			await addMessage({
				content: prompt,
				inputData: { regenerate: true },
			});
			// Don't set isGenerating to false here - let SSE handle it
			// The SSE will update the state when the assistant responds
		} catch (error) {
			console.error('Error regenerating code:', error);
			if (loadingTimeoutRef.current) {
				clearTimeout(loadingTimeoutRef.current);
			}
			setIsGenerating(false);
		}
	};

	return (
		<div className={`h-full flex ${className}`}>
			{generation && (
				<Resizable
					defaultSize={{ width: '20%', height: '100%' }}
					enable={{ right: true }}
					className='border-r border-border-secondary bg-surface-50'>
					<ChatPanel
						generation={generation}
						messages={messages}
						onSendMessage={handleSendMessage}
						onRegenerateCode={handleRegenerateCode}
						isGenerating={isGenerating}
					/>
				</Resizable>
			)}

			{!isDocumentationMode && (
				<Resizable
					defaultSize={{ width: '20%', height: '100%' }}
					enable={{ right: true, left: false }}
					className='border-r border-border-secondary'>
					<FileExplorer
						showHeader={false}
						collapsible
						className='h-full'
					/>
				</Resizable>
			)}
			<div className='flex w-full'>
				<Resizable
					defaultSize={{ width: '50%', height: '100%' }}
					enable={{ right: true, left: false }}
					className='border-r border-border-secondary'>
					<CodeEditor
						className='h-full'
						showActions
						onCodeChange={handleCodeChange}
					/>
				</Resizable>

				{/* Preview Panel */}
				<Resizable
					defaultSize={{ width: '50%', height: '100%' }}
					enable={{ left: false, right: false }}
					className='border-r border-border-secondary'>
					<PreviewPanel
						className='w-full'
						showControls
						autoStart={true}
						generationType={generation?.type || 'UI'}
						documentationContent={
							generation?.type === 'DOCUMENTATION' ? generationResult : ''
						}
						isStreaming={isStreaming}
						streamingContent={
							generation?.type === 'DOCUMENTATION' ? streamingResult : ''
						}
					/>
				</Resizable>
			</div>
		</div>
	);
};
